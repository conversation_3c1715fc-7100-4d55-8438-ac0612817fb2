//@version=5
strategy("REAL Market Structure", "REAL_MS", overlay=true, pyramiding=0, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// === CORE SETTINGS ===
htf_timeframe = input.timeframe("4h", "Higher Timeframe")
swing_length = input.int(10, "Swing Length", minval=5, maxval=20)
volume_threshold = input.float(1.5, "Volume Threshold", minval=1.0, maxval=3.0)

// === HTF BIAS ===
htf_close = request.security(syminfo.tickerid, htf_timeframe, close, lookahead=barmerge.lookahead_off)
htf_ema = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, 20), lookahead=barmerge.lookahead_off)

htf_bullish = htf_close > htf_ema
htf_bearish = htf_close < htf_ema

// === MARKET STRUCTURE ===
swing_high = ta.pivothigh(high, swing_length, swing_length)
swing_low = ta.pivotlow(low, swing_length, swing_length)

var float last_swing_high = na
var float last_swing_low = na

if not na(swing_high)
    last_swing_high := swing_high
if not na(swing_low)
    last_swing_low := swing_low

// Break of Structure
bullish_bos = not na(last_swing_high) and close > last_swing_high * 1.001
bearish_bos = not na(last_swing_low) and close < last_swing_low * 0.999

// === VOLUME ===
vol_ma = ta.sma(volume, 20)
volume_spike = volume > vol_ma * volume_threshold

// === SIGNALS ===
long_signal = htf_bullish and bullish_bos and volume_spike and barstate.isconfirmed
short_signal = htf_bearish and bearish_bos and volume_spike and barstate.isconfirmed

// === STRATEGY ===
if long_signal and strategy.position_size <= 0
    strategy.entry("Long", strategy.long, comment="🟢 LONG BOS")

if short_signal and strategy.position_size >= 0
    strategy.entry("Short", strategy.short, comment="🔴 SHORT BOS")

// === VISUALS ===
plot(htf_ema, "HTF EMA", color=htf_bullish ? color.green : color.red, linewidth=2)
plotshape(long_signal, "LONG", shape.labelup, location.belowbar, color.lime, text="🟢", size=size.normal)
plotshape(short_signal, "SHORT", shape.labeldown, location.abovebar, color.red, text="🔴", size=size.normal)
bgcolor(strategy.position_size > 0 ? color.new(color.green, 95) : strategy.position_size < 0 ? color.new(color.red, 95) : na)

// === DASHBOARD ===
if barstate.islast
    var table dashboard = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
    table.cell(dashboard, 0, 0, "REAL SCANNER", text_color=color.black, text_size=size.normal)
    table.cell(dashboard, 1, 0, syminfo.ticker, text_color=color.blue, text_size=size.normal)
    
    htf_status = htf_bullish ? "BULLISH" : htf_bearish ? "BEARISH" : "NEUTRAL"
    htf_color = htf_bullish ? color.green : htf_bearish ? color.red : color.gray
    table.cell(dashboard, 0, 1, "HTF BIAS:", text_color=color.black)
    table.cell(dashboard, 1, 1, htf_status, text_color=htf_color)
    
    vol_status = volume_spike ? "HIGH VOL" : "LOW VOL"
    vol_color = volume_spike ? color.green : color.gray
    table.cell(dashboard, 0, 2, "VOLUME:", text_color=color.black)
    table.cell(dashboard, 1, 2, vol_status, text_color=vol_color)
    
    pos_text = strategy.position_size > 0 ? "🟢 LONG" : strategy.position_size < 0 ? "🔴 SHORT" : "⚪ FLAT"
    pos_color = strategy.position_size > 0 ? color.green : strategy.position_size < 0 ? color.red : color.gray
    table.cell(dashboard, 0, 3, "POSITION:", text_color=color.black)
    table.cell(dashboard, 1, 3, pos_text, text_color=pos_color)
    
    status = long_signal ? "🟢 LONG SIGNAL" : short_signal ? "🔴 SHORT SIGNAL" : "👀 SCANNING"
    status_color = long_signal ? color.green : short_signal ? color.red : color.orange
    table.cell(dashboard, 0, 4, "STATUS:", text_color=color.black)
    table.cell(dashboard, 1, 4, status, text_color=status_color)

// === ALERTS ===
alertcondition(long_signal, "LONG BOS", "🟢 LONG: Break of Structure + HTF Bullish + Volume")
alertcondition(short_signal, "SHORT BOS", "🔴 SHORT: Break of Structure + HTF Bearish + Volume")
