


//@version=5
strategy(title="Clean MA Strategy", shorttitle="Clean_MA", overlay=true, pyramiding=0, default_qty_type=strategy.percent_of_equity, default_qty_value=10, calc_on_every_tick=false, process_orders_on_close=true, commission_type=strategy.commission.percent, commission_value=0.1)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// STRATEGY INPUTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === MAIN STRATEGY SETTINGS ===
G_STRATEGY = "═══ Strategy Settings ═══"
ma_type = input.string("EMA", "Moving Average Type", options=["SMA", "EMA", "WMA", "HMA"], group=G_STRATEGY)
fast_length = input.int(9, "Fast MA Length", minval=1, group=G_STRATEGY)
slow_length = input.int(21, "Slow MA Length", minval=1, group=G_STRATEGY)
use_trend_filter = input.bool(true, "Use Trend Filter (200 EMA)", group=G_STRATEGY)
trend_length = input.int(200, "Trend Filter Length", minval=50, group=G_STRATEGY)

// === ENTRY FILTERS ===
G_FILTERS = "═══ Entry Filters ═══"
use_rsi_filter = input.bool(true, "Use RSI Filter", group=G_FILTERS)
rsi_length = input.int(14, "RSI Length", minval=2, group=G_FILTERS)
rsi_oversold = input.int(30, "RSI Oversold Level", minval=10, maxval=40, group=G_FILTERS)
rsi_overbought = input.int(70, "RSI Overbought Level", minval=60, maxval=90, group=G_FILTERS)

use_volume_filter = input.bool(true, "Use Volume Filter", group=G_FILTERS)
volume_ma_length = input.int(20, "Volume MA Length", minval=5, group=G_FILTERS)
volume_multiplier = input.float(1.2, "Volume Multiplier", minval=1.0, maxval=3.0, step=0.1, group=G_FILTERS)
// === RISK MANAGEMENT ===
G_RISK = "═══ Risk Management ═══"
stop_loss_pct = input.float(2.0, "Stop Loss %", minval=0.1, maxval=10.0, step=0.1, group=G_RISK)
take_profit_1_pct = input.float(1.5, "Take Profit 1 %", minval=0.1, maxval=20.0, step=0.1, group=G_RISK)
take_profit_2_pct = input.float(3.0, "Take Profit 2 %", minval=0.1, maxval=20.0, step=0.1, group=G_RISK)
tp1_qty = input.float(50.0, "TP1 Quantity %", minval=10.0, maxval=100.0, step=5.0, group=G_RISK)
tp2_qty = input.float(50.0, "TP2 Quantity %", minval=10.0, maxval=100.0, step=5.0, group=G_RISK)

// === DISPLAY SETTINGS ===
G_DISPLAY = "═══ Display Settings ═══"
show_signals = input.bool(true, "Show Entry/Exit Signals", group=G_DISPLAY)
show_levels = input.bool(true, "Show TP/SL Levels", group=G_DISPLAY)
show_mas = input.bool(true, "Show Moving Averages", group=G_DISPLAY)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// STRATEGY CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === MOVING AVERAGE FUNCTION ===
get_ma(source, length, ma_type) =>
    switch ma_type
        "SMA" => ta.sma(source, length)
        "EMA" => ta.ema(source, length)
        "WMA" => ta.wma(source, length)
        "HMA" => ta.wma(2 * ta.wma(source, length / 2) - ta.wma(source, length), math.round(math.sqrt(length)))
        => ta.ema(source, length)

// === MOVING AVERAGES ===
fast_ma = get_ma(close, fast_length, ma_type)
slow_ma = get_ma(close, slow_length, ma_type)
trend_ma = ta.ema(close, trend_length)

// === BASIC SIGNALS ===
ma_cross_up = ta.crossover(fast_ma, slow_ma)
ma_cross_down = ta.crossunder(fast_ma, slow_ma)

// === ENTRY FILTERS ===
// Trend Filter
trend_bullish = not use_trend_filter or close > trend_ma
trend_bearish = not use_trend_filter or close < trend_ma

// RSI Filter
rsi = ta.rsi(close, rsi_length)
rsi_long_ok = not use_rsi_filter or rsi < rsi_overbought
rsi_short_ok = not use_rsi_filter or rsi > rsi_oversold

// Volume Filter
volume_ma = ta.sma(volume, volume_ma_length)
volume_ok = not use_volume_filter or volume > volume_ma * volume_multiplier

// === ENTRY CONDITIONS ===
long_condition = ma_cross_up and trend_bullish and rsi_long_ok and volume_ok
short_condition = ma_cross_down and trend_bearish and rsi_short_ok and volume_ok

// === POSITION TRACKING ===
var float entry_price = na
var float sl_price = na
var float tp1_price = na
var float tp2_price = na
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// STRATEGY LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === LONG ENTRIES ===
if strategy.position_size == 0 and long_condition and barstate.isconfirmed
    entry_price := close
    sl_price := entry_price * (1 - stop_loss_pct / 100)
    tp1_price := entry_price * (1 + take_profit_1_pct / 100)
    tp2_price := entry_price * (1 + take_profit_2_pct / 100)

    strategy.entry("Long", strategy.long, comment="Long Entry")

// === SHORT ENTRIES ===
if strategy.position_size == 0 and short_condition and barstate.isconfirmed
    entry_price := close
    sl_price := entry_price * (1 + stop_loss_pct / 100)
    tp1_price := entry_price * (1 - take_profit_1_pct / 100)
    tp2_price := entry_price * (1 - take_profit_2_pct / 100)

    strategy.entry("Short", strategy.short, comment="Short Entry")

// === EXITS ===
if strategy.position_size > 0  // Long position
    strategy.exit("TP1", "Long", qty_percent=tp1_qty, limit=tp1_price, stop=sl_price, comment_profit="TP1", comment_loss="SL")
    strategy.exit("TP2", "Long", qty_percent=tp2_qty, limit=tp2_price, stop=sl_price, comment_profit="TP2", comment_loss="SL")

if strategy.position_size < 0  // Short position
    strategy.exit("TP1", "Short", qty_percent=tp1_qty, limit=tp1_price, stop=sl_price, comment_profit="TP1", comment_loss="SL")
    strategy.exit("TP2", "Short", qty_percent=tp2_qty, limit=tp2_price, stop=sl_price, comment_profit="TP2", comment_loss="SL")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// VISUALS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === MOVING AVERAGES ===
plot(show_mas ? fast_ma : na, "Fast MA", color=color.blue, linewidth=2)
plot(show_mas ? slow_ma : na, "Slow MA", color=color.red, linewidth=2)
plot(show_mas and use_trend_filter ? trend_ma : na, "Trend Filter", color=color.gray, linewidth=1)

// === SIGNALS ===
plotshape(show_signals and long_condition, "Long Signal", shape.labelup, location.belowbar, color.green, text="LONG", textcolor=color.white, size=size.small)
plotshape(show_signals and short_condition, "Short Signal", shape.labeldown, location.abovebar, color.red, text="SHORT", textcolor=color.white, size=size.small)

// === LEVELS ===
plot(show_levels and strategy.position_size != 0 ? tp1_price : na, "TP1", color=color.green, style=plot.style_linebr, linewidth=1)
plot(show_levels and strategy.position_size != 0 ? tp2_price : na, "TP2", color=color.green, style=plot.style_linebr, linewidth=1)
plot(show_levels and strategy.position_size != 0 ? sl_price : na, "Stop Loss", color=color.red, style=plot.style_linebr, linewidth=1)
plot(show_levels and strategy.position_size != 0 ? entry_price : na, "Entry", color=color.blue, style=plot.style_linebr, linewidth=1)

// === BACKGROUND COLOR ===
bgcolor(strategy.position_size > 0 ? color.new(color.green, 95) : strategy.position_size < 0 ? color.new(color.red, 95) : na, title="Position Background")

// === WEBHOOK MESSAGES ===
G_MSG = "═══ Webhook Messages ═══"
i_leMsg = input.string("Long Entry", "Long Entry Message", group=G_MSG)
i_seMsg = input.string("Short Entry", "Short Entry Message", group=G_MSG)
i_lxMsgSL = input.string("Long SL", "Long Stop Loss Message", group=G_MSG)
i_sxMsgSL = input.string("Short SL", "Short Stop Loss Message", group=G_MSG)
i_lxMsgTP1 = input.string("Long TP1", "Long TP1 Message", group=G_MSG)
i_lxMsgTP2 = input.string("Long TP2", "Long TP2 Message", group=G_MSG)
i_sxMsgTP1 = input.string("Short TP1", "Short TP1 Message", group=G_MSG)
i_sxMsgTP2 = input.string("Short TP2", "Short TP2 Message", group=G_MSG)

// === ADDITIONAL VISUALS ===
i_barColOn = input.bool(true, "Show Bar Colors", group=G_DISPLAY)
barcolor(i_barColOn ? (close > open ? color.new(color.green, 50) : color.new(color.red, 50)) : na)

// === ALERTS ===
alertcondition(long_condition, "Long Entry Alert", "Long entry signal triggered")
alertcondition(short_condition, "Short Entry Alert", "Short entry signal triggered")

// === STRATEGY PERFORMANCE INFO ===
if barstate.islast
    var table infoTable = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
    table.cell(infoTable, 0, 0, "Strategy Info", text_color=color.black, text_size=size.normal)
    table.cell(infoTable, 0, 1, "Net Profit", text_color=color.black)
    table.cell(infoTable, 1, 1, str.tostring(strategy.netprofit, "#.##"), text_color=color.black)
    table.cell(infoTable, 0, 2, "Total Trades", text_color=color.black)
    table.cell(infoTable, 1, 2, str.tostring(strategy.closedtrades), text_color=color.black)
    table.cell(infoTable, 0, 3, "Win Rate", text_color=color.black)
    table.cell(infoTable, 1, 3, str.tostring(strategy.wintrades / strategy.closedtrades * 100, "#.#") + "%", text_color=color.black)
    table.cell(infoTable, 0, 4, "Position Size", text_color=color.black)
    table.cell(infoTable, 1, 4, str.tostring(strategy.position_size), text_color=color.black)
