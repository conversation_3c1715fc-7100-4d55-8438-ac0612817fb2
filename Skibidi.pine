


//@version=5
indicator("Market Scanner", "MS", overlay=true)

// Simple Settings
len = input.int(10, "Length")
vol_mult = input.float(1.5, "Volume")

// Basic Analysis
ema20 = ta.ema(close, 20)
ema50 = ta.ema(close, 50)
bull_trend = close > ema20 and ema20 > ema50
bear_trend = close < ema20 and ema20 < ema50

// Volume
vol_avg = ta.sma(volume, 20)
high_vol = volume > vol_avg * vol_mult

// Simple Signals
long_signal = bull_trend and high_vol and close > high[1]
short_signal = bear_trend and high_vol and close < low[1]
// Visuals
plot(ema20, "EMA20", color=bull_trend ? color.green : color.red, linewidth=2)
plot(ema50, "EMA50", color=color.gray, linewidth=1)
plotshape(long_signal, "LONG", shape.labelup, location.belowbar, color.lime, text="🟢", size=size.normal)
plotshape(short_signal, "SHORT", shape.labeldown, location.abovebar, color.red, text="🔴", size=size.normal)
bgcolor(long_signal ? color.new(color.green, 95) : short_signal ? color.new(color.red, 95) : na)

// Simple Dashboard
if barstate.islast
    var table info = table.new(position.top_right, 2, 4, bgcolor=color.white, border_width=1)
    table.cell(info, 0, 0, "SCANNER", text_color=color.black)
    table.cell(info, 1, 0, syminfo.ticker, text_color=color.blue)

    trend_txt = bull_trend ? "BULLISH" : bear_trend ? "BEARISH" : "NEUTRAL"
    trend_col = bull_trend ? color.green : bear_trend ? color.red : color.gray
    table.cell(info, 0, 1, "TREND:", text_color=color.black)
    table.cell(info, 1, 1, trend_txt, text_color=trend_col)

    vol_txt = high_vol ? "HIGH" : "LOW"
    vol_col = high_vol ? color.green : color.gray
    table.cell(info, 0, 2, "VOLUME:", text_color=color.black)
    table.cell(info, 1, 2, vol_txt, text_color=vol_col)

    signal_txt = long_signal ? "🟢 LONG" : short_signal ? "🔴 SHORT" : "⚪ WAIT"
    signal_col = long_signal ? color.green : short_signal ? color.red : color.gray
    table.cell(info, 0, 3, "SIGNAL:", text_color=color.black)
    table.cell(info, 1, 3, signal_txt, text_color=signal_col)

// Alerts
alertcondition(long_signal, "LONG", "LONG Signal")
alertcondition(short_signal, "SHORT", "SHORT Signal")
