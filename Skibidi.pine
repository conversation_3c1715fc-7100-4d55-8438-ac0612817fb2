


//@version=5
strategy("REAL Market Structure", "REAL_MS", overlay=true, pyramiding=0, default_qty_type=strategy.percent_of_equity, default_qty_value=100, calc_on_every_tick=false, process_orders_on_close=true)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// REAL TRADING STRATEGY - NO FAKE SIGNALS
// Based on Market Structure, Order Flow, and Multi-Timeframe Confluence
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === CORE SETTINGS ===
G_CORE = "═══ Core Strategy ═══"
htf_timeframe = input.timeframe("4h", "Higher Timeframe for Bias", group=G_CORE)
swing_length = input.int(10, "Swing Detection Length", minval=5, maxval=20, group=G_CORE)
volume_threshold = input.float(1.5, "Volume Spike Threshold", minval=1.0, maxval=3.0, step=0.1, group=G_CORE)
momentum_length = input.int(14, "Momentum Period", minval=10, maxval=30, group=G_CORE)

// === MARKET STRUCTURE ===
G_MS = "═══ Market Structure ═══"
bos_sensitivity = input.float(0.1, "Break of Structure Sensitivity %", minval=0.05, maxval=0.5, step=0.05, group=G_MS)
liquidity_zones = input.bool(true, "Show Liquidity Zones", group=G_MS)
show_structure = input.bool(true, "Show Market Structure", group=G_MS)

// === DASHBOARD ===
G_DASH = "═══ Dashboard ═══"
show_dashboard = input.bool(true, "Show Trading Dashboard", group=G_DASH)
dashboard_size = input.string("Normal", "Dashboard Size", options=["Small", "Normal", "Large"], group=G_DASH)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// REAL MARKET ANALYSIS - NO FAKE SIGNALS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === HIGHER TIMEFRAME BIAS ===
htf_close = request.security(syminfo.tickerid, htf_timeframe, close, lookahead=barmerge.lookahead_off)
htf_high = request.security(syminfo.tickerid, htf_timeframe, high, lookahead=barmerge.lookahead_off)
htf_low = request.security(syminfo.tickerid, htf_timeframe, low, lookahead=barmerge.lookahead_off)
htf_ema20 = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, 20), lookahead=barmerge.lookahead_off)

// HTF Bias
htf_bullish = htf_close > htf_ema20
htf_bearish = htf_close < htf_ema20
htf_bias = htf_bullish ? "BULLISH" : htf_bearish ? "BEARISH" : "NEUTRAL"

// === MARKET STRUCTURE DETECTION ===
// Swing Highs and Lows
swing_high = ta.pivothigh(high, swing_length, swing_length)
swing_low = ta.pivotlow(low, swing_length, swing_length)

// Track last swing points
var float last_swing_high = na
var float last_swing_low = na
var int last_high_bar = na
var int last_low_bar = na

if not na(swing_high)
    last_swing_high := swing_high
    last_high_bar := bar_index[swing_length]

if not na(swing_low)
    last_swing_low := swing_low
    last_low_bar := bar_index[swing_length]

// Break of Structure (BOS)
bos_threshold_high = last_swing_high * (1 + bos_sensitivity / 100)
bos_threshold_low = last_swing_low * (1 - bos_sensitivity / 100)

bullish_bos = not na(last_swing_high) and close > bos_threshold_high
bearish_bos = not na(last_swing_low) and close < bos_threshold_low

// === ORDER FLOW ANALYSIS ===
// Volume Analysis
vol_ma = ta.sma(volume, 20)
volume_spike = volume > vol_ma * volume_threshold
buying_pressure = close > open and volume_spike
selling_pressure = close < open and volume_spike

// Momentum
rsi = ta.rsi(close, momentum_length)
momentum_bullish = rsi > 50 and rsi[1] <= 50
momentum_bearish = rsi < 50 and rsi[1] >= 50

// === CONFLUENCE DETECTION ===
// Multiple confirmations required for entry
bullish_confluence = htf_bullish and bullish_bos and buying_pressure and momentum_bullish
bearish_confluence = htf_bearish and bearish_bos and selling_pressure and momentum_bearish

// === ENTRY CONDITIONS (REAL SIGNALS ONLY) ===
long_signal = bullish_confluence and barstate.isconfirmed
short_signal = bearish_confluence and barstate.isconfirmed

// === MARKET STATE ANALYSIS ===
market_trending = math.abs(close - close[20]) > ta.atr(14) * 2
market_ranging = not market_trending

// Current market condition
market_condition = market_trending ? (close > close[20] ? "TRENDING UP" : "TRENDING DOWN") : "RANGING"

// === WAITING CONDITIONS ===
waiting_htf_alignment = not (htf_bullish or htf_bearish)
waiting_structure_break = na(last_swing_high) or na(last_swing_low)
waiting_volume_confirmation = not volume_spike
waiting_momentum_shift = rsi > 30 and rsi < 70

// Dashboard status
var string current_status = "INITIALIZING"
if long_signal
    current_status := "🟢 LONG SIGNAL"
else if short_signal
    current_status := "🔴 SHORT SIGNAL"
else if waiting_htf_alignment
    current_status := "⏳ WAITING HTF ALIGNMENT"
else if waiting_structure_break
    current_status := "⏳ WAITING STRUCTURE BREAK"
else if waiting_volume_confirmation
    current_status := "⏳ WAITING VOLUME SPIKE"
else if market_ranging
    current_status := "⏳ MARKET RANGING - NO TRADE"
else
    current_status := "👀 SCANNING FOR SETUP"
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// REAL STRATEGY EXECUTION - PURE LONG/SHORT
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === POSITION MANAGEMENT ===
// Close opposite position before opening new one
if long_signal and strategy.position_size < 0
    strategy.close("Short", comment="Close Short for Long")

if short_signal and strategy.position_size > 0
    strategy.close("Long", comment="Close Long for Short")

// === ENTRIES (NO STOP LOSS, NO TAKE PROFIT) ===
if long_signal
    strategy.entry("Long", strategy.long, qty=100, comment="🟢 LONG BOS")

if short_signal
    strategy.entry("Short", strategy.short, qty=100, comment="🔴 SHORT BOS")

// === POSITION TRACKING ===
var float entry_price = na
var int entry_bar = na
var string entry_reason = "No Position"

if long_signal
    entry_price := close
    entry_bar := bar_index
    entry_reason := "Bullish BOS + HTF Bull + Vol"

if short_signal
    entry_price := close
    entry_bar := bar_index
    entry_reason := "Bearish BOS + HTF Bear + Vol"

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// REAL MARKET VISUALS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === HTF EMA ===
plot(htf_ema20, "HTF EMA20", color=htf_bullish ? color.green : color.red, linewidth=2, style=plot.style_line)

// === MARKET STRUCTURE ===
plotshape(show_structure and not na(swing_high), "Swing High", shape.triangledown, location.abovebar, color.red, size=size.tiny)
plotshape(show_structure and not na(swing_low), "Swing Low", shape.triangleup, location.belowbar, color.green, size=size.tiny)

// === BREAK OF STRUCTURE SIGNALS ===
plotshape(long_signal, "LONG BOS", shape.labelup, location.belowbar, color.lime, text="🟢 LONG", textcolor=color.white, size=size.normal)
plotshape(short_signal, "SHORT BOS", shape.labeldown, location.abovebar, color.red, text="🔴 SHORT", textcolor=color.white, size=size.normal)

// === LIQUIDITY ZONES ===
var line high_line = na
var line low_line = na
var line entry_line = na

if liquidity_zones and not na(swing_high)
    line.delete(high_line)
    high_line := line.new(bar_index[swing_length], swing_high, bar_index + 20, swing_high, color=color.red, style=line.style_dashed, width=1, extend=extend.right)

if liquidity_zones and not na(swing_low)
    line.delete(low_line)
    low_line := line.new(bar_index[swing_length], swing_low, bar_index + 20, swing_low, color=color.green, style=line.style_dashed, width=1, extend=extend.right)

// === ENTRY PRICE LINE ===
if long_signal or short_signal
    line.delete(entry_line)
    entry_line := line.new(bar_index, close, bar_index + 20, close, color=color.blue, style=line.style_solid, width=2, extend=extend.right)

// === POSITION BACKGROUND ===
bgcolor(strategy.position_size > 0 ? color.new(color.green, 95) : strategy.position_size < 0 ? color.new(color.red, 95) : na)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// DYNAMIC TRADING DASHBOARD
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

if show_dashboard and barstate.islast
    // Dashboard size settings
    table_size = dashboard_size == "Small" ? size.small : dashboard_size == "Large" ? size.large : size.normal

    // Main dashboard
    var table dashboard = table.new(position.top_right, 2, 10, bgcolor=color.new(color.black, 80), border_width=1)

    // Header
    table.cell(dashboard, 0, 0, "🎯 REAL MARKET SCANNER", text_color=color.white, text_size=size.large, bgcolor=color.new(color.blue, 50))
    table.cell(dashboard, 1, 0, syminfo.ticker, text_color=color.yellow, text_size=size.large, bgcolor=color.new(color.blue, 50))

    // Current Status
    status_color = long_signal ? color.lime : short_signal ? color.red : color.orange
    table.cell(dashboard, 0, 1, "STATUS:", text_color=color.white, text_size=table_size)
    table.cell(dashboard, 1, 1, current_status, text_color=status_color, text_size=table_size)

    // HTF Bias
    htf_color = htf_bullish ? color.lime : htf_bearish ? color.red : color.gray
    table.cell(dashboard, 0, 2, "HTF BIAS:", text_color=color.white, text_size=table_size)
    table.cell(dashboard, 1, 2, htf_bias, text_color=htf_color, text_size=table_size)

    // Market Condition
    condition_color = market_trending ? color.yellow : color.gray
    table.cell(dashboard, 0, 3, "MARKET:", text_color=color.white, text_size=table_size)
    table.cell(dashboard, 1, 3, market_condition, text_color=condition_color, text_size=table_size)

    // Volume Status
    vol_color = volume_spike ? color.lime : color.gray
    vol_status = volume_spike ? "HIGH VOLUME" : "LOW VOLUME"
    table.cell(dashboard, 0, 4, "VOLUME:", text_color=color.white, text_size=table_size)
    table.cell(dashboard, 1, 4, vol_status, text_color=vol_color, text_size=table_size)

    // RSI Momentum
    rsi_color = rsi > 70 ? color.red : rsi < 30 ? color.lime : color.yellow
    table.cell(dashboard, 0, 5, "RSI:", text_color=color.white, text_size=table_size)
    table.cell(dashboard, 1, 5, str.tostring(math.round(rsi)), text_color=rsi_color, text_size=table_size)

    // Current Position
    pos_text = strategy.position_size > 0 ? "🟢 LONG" : strategy.position_size < 0 ? "🔴 SHORT" : "⚪ FLAT"
    pos_color = strategy.position_size > 0 ? color.lime : strategy.position_size < 0 ? color.red : color.gray
    table.cell(dashboard, 0, 6, "POSITION:", text_color=color.white, text_size=table_size)
    table.cell(dashboard, 1, 6, pos_text, text_color=pos_color, text_size=table_size)

    // Entry Price
    if strategy.position_size != 0 and not na(entry_price)
        table.cell(dashboard, 0, 7, "ENTRY:", text_color=color.white, text_size=table_size)
        table.cell(dashboard, 1, 7, str.tostring(entry_price, "#.####"), text_color=color.blue, text_size=table_size)

        // P&L
        current_pnl = strategy.position_size > 0 ? (close - entry_price) : strategy.position_size < 0 ? (entry_price - close) : 0
        pnl_pct = entry_price != 0 ? (current_pnl / entry_price) * 100 : 0
        pnl_color = current_pnl > 0 ? color.lime : current_pnl < 0 ? color.red : color.gray
        table.cell(dashboard, 0, 8, "P&L:", text_color=color.white, text_size=table_size)
        table.cell(dashboard, 1, 8, str.tostring(pnl_pct, "#.##") + "%", text_color=pnl_color, text_size=table_size)

    // Last Signal Info
    if not na(entry_reason) and entry_reason != ""
        table.cell(dashboard, 0, 9, "REASON:", text_color=color.white, text_size=size.tiny)
        table.cell(dashboard, 1, 9, entry_reason, text_color=color.yellow, text_size=size.tiny)

// === ALERTS ===
alertcondition(long_signal, "LONG BOS", "🟢 LONG: Break of Structure + HTF Bullish + Volume Spike")
alertcondition(short_signal, "SHORT BOS", "🔴 SHORT: Break of Structure + HTF Bearish + Volume Spike")

// === BAR COLORS ===
barcolor(volume_spike ? (close > open ? color.lime : color.red) : (close > open ? color.green : color.maroon))
