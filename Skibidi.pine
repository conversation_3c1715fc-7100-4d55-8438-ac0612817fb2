


//@version=5
strategy("PROFESSIONAL TRADING SYSTEM", "PTS", overlay=true, pyramiding=0, default_qty_type=strategy.percent_of_equity, default_qty_value=100, calc_on_every_tick=false, process_orders_on_close=true, commission_type=strategy.commission.percent, commission_value=0.1)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PROFESSIONAL TRADING SYSTEM - REAL MONEY READY
// Multi-Timeframe Analysis + Smart Risk Management + Professional UI
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === STRATEGY SETTINGS ===
G_STRATEGY = "═══ Strategy Settings ═══"
strategy_mode = input.string("AGGRESSIVE", "Trading Mode", options=["CONSERVATIVE", "BALANCED", "AGGRESSIVE"], group=G_STRATEGY)
htf_timeframe = input.timeframe("4h", "Higher Timeframe", group=G_STRATEGY)
entry_timeframe = input.timeframe("15", "Entry Timeframe", group=G_STRATEGY)
use_smart_entries = input.bool(true, "Smart Entry System", group=G_STRATEGY)
use_confluence = input.bool(true, "Multi-Confluence Filter", group=G_STRATEGY)

// === RISK MANAGEMENT ===
G_RISK = "═══ Risk Management ═══"
risk_per_trade = input.float(2.0, "Risk Per Trade %", minval=0.5, maxval=10.0, step=0.5, group=G_RISK)
use_dynamic_sl = input.bool(true, "Dynamic Stop Loss", group=G_RISK)
sl_atr_mult = input.float(2.0, "SL ATR Multiplier", minval=1.0, maxval=5.0, step=0.5, group=G_RISK)
tp_ratio = input.float(2.0, "Take Profit Ratio", minval=1.0, maxval=5.0, step=0.5, group=G_RISK)
use_trailing_sl = input.bool(true, "Trailing Stop Loss", group=G_RISK)
trailing_atr_mult = input.float(1.5, "Trailing ATR Mult", minval=1.0, maxval=3.0, step=0.1, group=G_RISK)

// === TECHNICAL ANALYSIS ===
G_TECH = "═══ Technical Analysis ═══"
ema_fast = input.int(12, "Fast EMA", minval=5, maxval=50, group=G_TECH)
ema_slow = input.int(26, "Slow EMA", minval=20, maxval=100, group=G_TECH)
ema_filter = input.int(200, "Trend Filter EMA", minval=100, maxval=300, group=G_TECH)
rsi_length = input.int(14, "RSI Length", minval=10, maxval=30, group=G_TECH)
volume_factor = input.float(1.5, "Volume Factor", minval=1.0, maxval=3.0, step=0.1, group=G_TECH)
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PROFESSIONAL TRADING LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === MULTI-TIMEFRAME ANALYSIS ===
htf_ema_fast = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_fast), lookahead=barmerge.lookahead_off)
htf_ema_slow = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_slow), lookahead=barmerge.lookahead_off)
htf_trend_bull = htf_ema_fast > htf_ema_slow
htf_trend_bear = htf_ema_fast < htf_ema_slow

// Current timeframe analysis
ema_fast_current = ta.ema(close, ema_fast)
ema_slow_current = ta.ema(close, ema_slow)
ema_filter_current = ta.ema(close, ema_filter)

// Trend Analysis
strong_uptrend = close > ema_fast_current and ema_fast_current > ema_slow_current and ema_slow_current > ema_filter_current
strong_downtrend = close < ema_fast_current and ema_fast_current < ema_slow_current and ema_slow_current < ema_filter_current
trend_bullish = close > ema_filter_current and htf_trend_bull
trend_bearish = close < ema_filter_current and htf_trend_bear

// === MOMENTUM & VOLUME ANALYSIS ===
rsi = ta.rsi(close, rsi_length)
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70
rsi_bullish = rsi > 50 and rsi[1] <= 50
rsi_bearish = rsi < 50 and rsi[1] >= 50

volume_ma = ta.sma(volume, 20)
volume_spike = volume > volume_ma * volume_factor
volume_bullish = close > open and volume_spike
volume_bearish = close < open and volume_spike

// === SMART ENTRY SYSTEM ===
atr = ta.atr(14)
price_action_bull = close > high[1] and close > open
price_action_bear = close < low[1] and close < open

// Confluence scoring system
confluence_score_long = 0
confluence_score_short = 0

if trend_bullish
    confluence_score_long += 3
if strong_uptrend
    confluence_score_long += 2
if rsi_bullish or rsi_oversold
    confluence_score_long += 1
if volume_bullish
    confluence_score_long += 2
if price_action_bull
    confluence_score_long += 1

if trend_bearish
    confluence_score_short += 3
if strong_downtrend
    confluence_score_short += 2
if rsi_bearish or rsi_overbought
    confluence_score_short += 1
if volume_bearish
    confluence_score_short += 2
if price_action_bear
    confluence_score_short += 1

// Entry thresholds based on strategy mode
min_confluence = strategy_mode == "CONSERVATIVE" ? 7 : strategy_mode == "BALANCED" ? 5 : 3

// === ENTRY SIGNALS ===
long_entry = use_confluence ? confluence_score_long >= min_confluence : trend_bullish and volume_bullish and price_action_bull
short_entry = use_confluence ? confluence_score_short >= min_confluence : trend_bearish and volume_bearish and price_action_bear

// Smart entry timing
smart_long = use_smart_entries ? long_entry and barstate.isconfirmed and not long_entry[1] : long_entry
smart_short = use_smart_entries ? short_entry and barstate.isconfirmed and not short_entry[1] : short_entry

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PROFESSIONAL STRATEGY EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === POSITION SIZING ===
account_size = strategy.equity
risk_amount = account_size * (risk_per_trade / 100)

// === DYNAMIC STOP LOSS & TAKE PROFIT ===
var float entry_price = na
var float stop_loss = na
var float take_profit = na
var float trailing_stop = na

// Calculate position size based on risk
calculate_position_size(entry_price, stop_loss) =>
    risk_per_share = math.abs(entry_price - stop_loss)
    position_size = risk_per_share > 0 ? risk_amount / risk_per_share : 0
    math.min(position_size, account_size * 0.95)

// === LONG ENTRIES ===
if smart_long and strategy.position_size == 0
    entry_price := close
    stop_loss := use_dynamic_sl ? close - (atr * sl_atr_mult) : close * 0.98
    take_profit := close + (math.abs(close - stop_loss) * tp_ratio)
    trailing_stop := stop_loss

    pos_size = calculate_position_size(entry_price, stop_loss)
    strategy.entry("LONG", strategy.long, qty=pos_size, comment="🟢 LONG")

// === SHORT ENTRIES ===
if smart_short and strategy.position_size == 0
    entry_price := close
    stop_loss := use_dynamic_sl ? close + (atr * sl_atr_mult) : close * 1.02
    take_profit := close - (math.abs(stop_loss - close) * tp_ratio)
    trailing_stop := stop_loss

    pos_size = calculate_position_size(entry_price, stop_loss)
    strategy.entry("SHORT", strategy.short, qty=pos_size, comment="🔴 SHORT")

// === TRAILING STOP LOGIC ===
if strategy.position_size > 0 and use_trailing_sl
    new_trailing = close - (atr * trailing_atr_mult)
    trailing_stop := math.max(trailing_stop, new_trailing)

if strategy.position_size < 0 and use_trailing_sl
    new_trailing = close + (atr * trailing_atr_mult)
    trailing_stop := math.min(trailing_stop, new_trailing)

// === EXIT MANAGEMENT ===
if strategy.position_size > 0
    if use_trailing_sl
        strategy.exit("LONG EXIT", "LONG", stop=trailing_stop, limit=take_profit, comment_profit="🎯 TP", comment_loss="🛑 TSL")
    else
        strategy.exit("LONG EXIT", "LONG", stop=stop_loss, limit=take_profit, comment_profit="🎯 TP", comment_loss="🛑 SL")

if strategy.position_size < 0
    if use_trailing_sl
        strategy.exit("SHORT EXIT", "SHORT", stop=trailing_stop, limit=take_profit, comment_profit="🎯 TP", comment_loss="🛑 TSL")
    else
        strategy.exit("SHORT EXIT", "SHORT", stop=stop_loss, limit=take_profit, comment_profit="🎯 TP", comment_loss="🛑 SL")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PROFESSIONAL UI & VISUALS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === MOVING AVERAGES ===
plot(ema_fast_current, "Fast EMA", color=trend_bullish ? color.lime : color.red, linewidth=2)
plot(ema_slow_current, "Slow EMA", color=trend_bullish ? color.green : color.maroon, linewidth=2)
plot(ema_filter_current, "Trend Filter", color=color.gray, linewidth=1)

// === HTF TREND LINES ===
plot(htf_ema_fast, "HTF Fast", color=htf_trend_bull ? color.aqua : color.orange, linewidth=1, style=plot.style_circles)
plot(htf_ema_slow, "HTF Slow", color=htf_trend_bull ? color.blue : color.red, linewidth=1, style=plot.style_circles)

// === ENTRY SIGNALS ===
plotshape(smart_long, "LONG ENTRY", shape.labelup, location.belowbar, color.lime, text="🚀 LONG", textcolor=color.white, size=size.normal)
plotshape(smart_short, "SHORT ENTRY", shape.labeldown, location.abovebar, color.red, text="🚀 SHORT", textcolor=color.white, size=size.normal)

// === SUPPORT/RESISTANCE LEVELS ===
plot(strategy.position_size != 0 ? entry_price : na, "Entry Price", color=color.blue, linewidth=2, style=plot.style_linebr)
plot(strategy.position_size != 0 ? stop_loss : na, "Stop Loss", color=color.red, linewidth=2, style=plot.style_linebr)
plot(strategy.position_size != 0 ? take_profit : na, "Take Profit", color=color.green, linewidth=2, style=plot.style_linebr)
plot(strategy.position_size != 0 and use_trailing_sl ? trailing_stop : na, "Trailing Stop", color=color.orange, linewidth=1, style=plot.style_linebr)

// === BACKGROUND COLORS ===
bgcolor(strategy.position_size > 0 ? color.new(color.green, 97) : strategy.position_size < 0 ? color.new(color.red, 97) : na)
bgcolor(smart_long ? color.new(color.lime, 90) : smart_short ? color.new(color.red, 90) : na)

// === CONFLUENCE VISUALIZATION ===
barcolor(confluence_score_long >= min_confluence ? color.lime : confluence_score_short >= min_confluence ? color.red : color.gray)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PROFESSIONAL TRADING DASHBOARD
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

if barstate.islast
    // Main Trading Dashboard
    var table dashboard = table.new(position.top_right, 3, 15, bgcolor=color.new(color.black, 85), border_width=2, border_color=color.white)

    // Header
    table.cell(dashboard, 0, 0, "🎯 PROFESSIONAL TRADING SYSTEM", text_color=color.white, text_size=size.large, bgcolor=color.new(color.blue, 70))
    table.cell(dashboard, 1, 0, syminfo.ticker, text_color=color.yellow, text_size=size.large, bgcolor=color.new(color.blue, 70))
    table.cell(dashboard, 2, 0, timeframe.period, text_color=color.yellow, text_size=size.large, bgcolor=color.new(color.blue, 70))

    // Strategy Mode
    mode_color = strategy_mode == "AGGRESSIVE" ? color.red : strategy_mode == "BALANCED" ? color.yellow : color.green
    table.cell(dashboard, 0, 1, "MODE:", text_color=color.white, text_size=size.normal)
    table.cell(dashboard, 1, 1, strategy_mode, text_color=mode_color, text_size=size.normal)
    table.cell(dashboard, 2, 1, "RISK: " + str.tostring(risk_per_trade, "#.#") + "%", text_color=color.orange, text_size=size.normal)

    // Market Analysis
    htf_status = htf_trend_bull ? "🟢 BULLISH" : htf_trend_bear ? "🔴 BEARISH" : "⚪ NEUTRAL"
    htf_color = htf_trend_bull ? color.lime : htf_trend_bear ? color.red : color.gray
    table.cell(dashboard, 0, 2, "HTF TREND:", text_color=color.white, text_size=size.normal)
    table.cell(dashboard, 1, 2, htf_status, text_color=htf_color, text_size=size.normal)

    trend_status = strong_uptrend ? "🚀 STRONG UP" : strong_downtrend ? "🚀 STRONG DOWN" : trend_bullish ? "📈 BULLISH" : trend_bearish ? "📉 BEARISH" : "➡️ SIDEWAYS"
    trend_color = strong_uptrend ? color.lime : strong_downtrend ? color.red : trend_bullish ? color.green : trend_bearish ? color.maroon : color.gray
    table.cell(dashboard, 2, 2, trend_status, text_color=trend_color, text_size=size.normal)

    // Confluence Scores
    table.cell(dashboard, 0, 3, "CONFLUENCE:", text_color=color.white, text_size=size.normal)
    long_conf_color = confluence_score_long >= min_confluence ? color.lime : confluence_score_long >= (min_confluence - 2) ? color.yellow : color.gray
    short_conf_color = confluence_score_short >= min_confluence ? color.red : confluence_score_short >= (min_confluence - 2) ? color.orange : color.gray
    table.cell(dashboard, 1, 3, "LONG: " + str.tostring(confluence_score_long), text_color=long_conf_color, text_size=size.normal)
    table.cell(dashboard, 2, 3, "SHORT: " + str.tostring(confluence_score_short), text_color=short_conf_color, text_size=size.normal)

    // Technical Indicators
    rsi_color = rsi > 70 ? color.red : rsi < 30 ? color.lime : color.yellow
    table.cell(dashboard, 0, 4, "RSI:", text_color=color.white, text_size=size.normal)
    table.cell(dashboard, 1, 4, str.tostring(math.round(rsi)), text_color=rsi_color, text_size=size.normal)

    vol_status = volume_spike ? "🔥 HIGH" : "📊 NORMAL"
    vol_color = volume_spike ? color.lime : color.gray
    table.cell(dashboard, 2, 4, vol_status, text_color=vol_color, text_size=size.normal)

    // Current Position
    pos_text = strategy.position_size > 0 ? "🟢 LONG ACTIVE" : strategy.position_size < 0 ? "🔴 SHORT ACTIVE" : "⚪ NO POSITION"
    pos_color = strategy.position_size > 0 ? color.lime : strategy.position_size < 0 ? color.red : color.gray
    table.cell(dashboard, 0, 5, "POSITION:", text_color=color.white, text_size=size.normal)
    table.cell(dashboard, 1, 5, pos_text, text_color=pos_color, text_size=size.normal)

    // Position Details
    if strategy.position_size != 0
        pnl = strategy.position_size > 0 ? (close - entry_price) : (entry_price - close)
        pnl_pct = entry_price != 0 ? (pnl / entry_price) * 100 : 0
        pnl_color = pnl > 0 ? color.lime : pnl < 0 ? color.red : color.gray
        table.cell(dashboard, 2, 5, str.tostring(pnl_pct, "#.##") + "%", text_color=pnl_color, text_size=size.normal)

        table.cell(dashboard, 0, 6, "ENTRY:", text_color=color.white, text_size=size.small)
        table.cell(dashboard, 1, 6, str.tostring(entry_price, "#.####"), text_color=color.blue, text_size=size.small)

        table.cell(dashboard, 0, 7, "STOP:", text_color=color.white, text_size=size.small)
        table.cell(dashboard, 1, 7, str.tostring(use_trailing_sl ? trailing_stop : stop_loss, "#.####"), text_color=color.red, text_size=size.small)

        table.cell(dashboard, 0, 8, "TARGET:", text_color=color.white, text_size=size.small)
        table.cell(dashboard, 1, 8, str.tostring(take_profit, "#.####"), text_color=color.green, text_size=size.small)

    // Signal Status
    signal_status = smart_long ? "🚀 LONG SIGNAL!" : smart_short ? "🚀 SHORT SIGNAL!" : confluence_score_long >= (min_confluence - 1) ? "👀 LONG SETUP" : confluence_score_short >= (min_confluence - 1) ? "👀 SHORT SETUP" : "⏳ WAITING..."
    signal_color = smart_long ? color.lime : smart_short ? color.red : color.orange
    table.cell(dashboard, 0, 9, "STATUS:", text_color=color.white, text_size=size.normal)
    table.cell(dashboard, 1, 9, signal_status, text_color=signal_color, text_size=size.normal)

    // Performance Stats
    win_rate = strategy.closedtrades > 0 ? (strategy.wintrades / strategy.closedtrades) * 100 : 0
    table.cell(dashboard, 0, 10, "TRADES:", text_color=color.white, text_size=size.small)
    table.cell(dashboard, 1, 10, str.tostring(strategy.closedtrades), text_color=color.yellow, text_size=size.small)
    table.cell(dashboard, 2, 10, "WIN: " + str.tostring(win_rate, "#.#") + "%", text_color=win_rate > 50 ? color.lime : color.red, text_size=size.small)

    table.cell(dashboard, 0, 11, "P&L:", text_color=color.white, text_size=size.small)
    pnl_total_color = strategy.netprofit > 0 ? color.lime : strategy.netprofit < 0 ? color.red : color.gray
    table.cell(dashboard, 1, 11, str.tostring(strategy.netprofit, "#.##"), text_color=pnl_total_color, text_size=size.small)

// === ALERTS ===
alertcondition(smart_long, "LONG ENTRY", "🚀 LONG ENTRY: Multi-Confluence Signal Triggered")
alertcondition(smart_short, "SHORT ENTRY", "🚀 SHORT ENTRY: Multi-Confluence Signal Triggered")
alertcondition(strategy.position_size[1] != 0 and strategy.position_size == 0, "POSITION CLOSED", "Position Closed - Check Results")
