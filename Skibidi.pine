


//@version=5
indicator("REAL Market Scanner", "REAL_MS", overlay=true)

// Settings
htf_tf = input.timeframe("4h", "Higher Timeframe")
swing_len = input.int(10, "Swing Length", minval=5, maxval=20)
vol_mult = input.float(1.5, "Volume Multiplier", minval=1.0, maxval=3.0)
show_dash = input.bool(true, "Show Dashboard")
show_swings = input.bool(true, "Show Swings")

// HTF Analysis
htf_close = request.security(syminfo.tickerid, htf_tf, close, lookahead=barmerge.lookahead_off)
htf_ema = request.security(syminfo.tickerid, htf_tf, ta.ema(close, 20), lookahead=barmerge.lookahead_off)
htf_bull = htf_close > htf_ema
htf_bear = htf_close < htf_ema

// Market Structure
swing_h = ta.pivothigh(high, swing_len, swing_len)
swing_l = ta.pivotlow(low, swing_len, swing_len)

var float last_high = na
var float last_low = na

if not na(swing_h)
    last_high := swing_h
if not na(swing_l)
    last_low := swing_l

// Break of Structure
bull_bos = not na(last_high) and close > last_high * 1.001
bear_bos = not na(last_low) and close < last_low * 0.999

// Volume
vol_avg = ta.sma(volume, 20)
vol_spike = volume > vol_avg * vol_mult

// Signals
long_sig = htf_bull and bull_bos and vol_spike and barstate.isconfirmed
short_sig = htf_bear and bear_bos and vol_spike and barstate.isconfirmed

// Status
status = long_sig ? "🟢 LONG" : short_sig ? "🔴 SHORT" : htf_bull ? "👀 SCAN LONG" : htf_bear ? "👀 SCAN SHORT" : "⏳ WAIT HTF"
// Signal Tracking
var string pos = "FLAT"
var float sig_price = na

if long_sig
    pos := "LONG"
    sig_price := close

if short_sig
    pos := "SHORT"
    sig_price := close

// Visuals
plot(htf_ema, "HTF EMA", color=htf_bull ? color.green : color.red, linewidth=2)
plotshape(show_swings and not na(swing_h), "H", shape.triangledown, location.abovebar, color.red, size=size.tiny)
plotshape(show_swings and not na(swing_l), "L", shape.triangleup, location.belowbar, color.green, size=size.tiny)
plotshape(long_sig, "LONG", shape.labelup, location.belowbar, color.lime, text="🟢", size=size.normal)
plotshape(short_sig, "SHORT", shape.labeldown, location.abovebar, color.red, text="🔴", size=size.normal)
bgcolor(pos == "LONG" ? color.new(color.green, 95) : pos == "SHORT" ? color.new(color.red, 95) : na)

// Dashboard
if show_dash and barstate.islast
    var table dash = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
    table.cell(dash, 0, 0, "REAL SCANNER", text_color=color.black)
    table.cell(dash, 1, 0, syminfo.ticker, text_color=color.blue)

    htf_txt = htf_bull ? "BULLISH" : htf_bear ? "BEARISH" : "NEUTRAL"
    htf_col = htf_bull ? color.green : htf_bear ? color.red : color.gray
    table.cell(dash, 0, 1, "HTF:", text_color=color.black)
    table.cell(dash, 1, 1, htf_txt, text_color=htf_col)

    vol_txt = vol_spike ? "HIGH" : "LOW"
    vol_col = vol_spike ? color.green : color.gray
    table.cell(dash, 0, 2, "VOL:", text_color=color.black)
    table.cell(dash, 1, 2, vol_txt, text_color=vol_col)

    pos_txt = pos == "LONG" ? "🟢 LONG" : pos == "SHORT" ? "🔴 SHORT" : "⚪ FLAT"
    pos_col = pos == "LONG" ? color.green : pos == "SHORT" ? color.red : color.gray
    table.cell(dash, 0, 3, "SIGNAL:", text_color=color.black)
    table.cell(dash, 1, 3, pos_txt, text_color=pos_col)

    if not na(sig_price)
        table.cell(dash, 0, 4, "PRICE:", text_color=color.black)
        table.cell(dash, 1, 4, str.tostring(sig_price, "#.####"), text_color=color.blue)

    stat_col = long_sig ? color.green : short_sig ? color.red : color.orange
    table.cell(dash, 0, 5, "STATUS:", text_color=color.black)
    table.cell(dash, 1, 5, status, text_color=stat_col)

// Alerts
alertcondition(long_sig, "LONG", "🟢 LONG: HTF Bull + BOS + Volume")
alertcondition(short_sig, "SHORT", "🔴 SHORT: HTF Bear + BOS + Volume")
